import 'package:flutter/material.dart';

class ResponsiveUtils {
  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < 600;
  }

  static bool isMediumScreen(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 600 && width < 1200;
  }

  static bool isLargeScreen(BuildContext context) {
    return MediaQuery.of(context).size.width >= 1200;
  }

  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  static double getSafeAreaHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height - 
           mediaQuery.padding.top - 
           mediaQuery.padding.bottom;
  }

  static double getSafeAreaWidth(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.width - 
           mediaQuery.padding.left - 
           mediaQuery.padding.right;
  }

  // Calculate optimal cell size based on screen dimensions
  static double calculateCellSize(BuildContext context) {
    final screenWidth = getSafeAreaWidth(context);
    final screenHeight = getSafeAreaHeight(context);
    final isPortrait = ResponsiveUtils.isPortrait(context);

    if (isPortrait) {
      // In portrait mode, use 80% of screen width for game area
      final gameAreaWidth = screenWidth * 0.8;
      return gameAreaWidth / 10; // 10 columns in Tetris
    } else {
      // In landscape mode, use 50% of screen width for game area
      final gameAreaWidth = screenWidth * 0.5;
      final gameAreaHeight = screenHeight * 0.8;
      
      // Choose the smaller dimension to ensure game fits
      final cellSizeByWidth = gameAreaWidth / 10;
      final cellSizeByHeight = gameAreaHeight / 20; // 20 rows in Tetris
      
      return cellSizeByWidth < cellSizeByHeight ? cellSizeByWidth : cellSizeByHeight;
    }
  }

  // Get appropriate font size based on screen size
  static double getFontSize(BuildContext context, FontSizeType type) {
    final screenWidth = getScreenWidth(context);
    
    double baseFontSize;
    switch (type) {
      case FontSizeType.small:
        baseFontSize = 12;
        break;
      case FontSizeType.medium:
        baseFontSize = 16;
        break;
      case FontSizeType.large:
        baseFontSize = 20;
        break;
      case FontSizeType.extraLarge:
        baseFontSize = 24;
        break;
      case FontSizeType.title:
        baseFontSize = 32;
        break;
    }

    // Scale font size based on screen width
    if (screenWidth < 360) {
      return baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      return baseFontSize * 1.2;
    }
    
    return baseFontSize;
  }

  // Get appropriate padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    final screenWidth = getScreenWidth(context);
    
    if (screenWidth < 360) {
      return const EdgeInsets.all(8);
    } else if (screenWidth > 600) {
      return const EdgeInsets.all(24);
    }
    
    return const EdgeInsets.all(16);
  }

  // Get appropriate margin based on screen size
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    final screenWidth = getScreenWidth(context);
    
    if (screenWidth < 360) {
      return const EdgeInsets.all(4);
    } else if (screenWidth > 600) {
      return const EdgeInsets.all(16);
    }
    
    return const EdgeInsets.all(8);
  }

  // Get appropriate button size based on screen size
  static Size getButtonSize(BuildContext context) {
    final screenWidth = getScreenWidth(context);
    
    if (screenWidth < 360) {
      return const Size(60, 60);
    } else if (screenWidth > 600) {
      return const Size(80, 80);
    }
    
    return const Size(70, 70);
  }

  // Get appropriate icon size based on screen size
  static double getIconSize(BuildContext context) {
    final screenWidth = getScreenWidth(context);
    
    if (screenWidth < 360) {
      return 20;
    } else if (screenWidth > 600) {
      return 32;
    }
    
    return 24;
  }

  // Check if device has enough space for landscape layout
  static bool shouldUseLandscapeLayout(BuildContext context) {
    final screenWidth = getScreenWidth(context);
    final screenHeight = getScreenHeight(context);
    
    return isLandscape(context) && 
           screenWidth > 600 && 
           screenHeight > 400;
  }

  // Get game area constraints
  static BoxConstraints getGameAreaConstraints(BuildContext context) {
    final screenWidth = getSafeAreaWidth(context);
    final screenHeight = getSafeAreaHeight(context);
    final isPortrait = ResponsiveUtils.isPortrait(context);

    if (isPortrait) {
      final maxWidth = screenWidth * 0.9;
      final maxHeight = screenHeight * 0.6;
      return BoxConstraints(
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        minWidth: 200,
        minHeight: 400,
      );
    } else {
      final maxWidth = screenWidth * 0.5;
      final maxHeight = screenHeight * 0.8;
      return BoxConstraints(
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        minWidth: 200,
        minHeight: 400,
      );
    }
  }

  // Get control panel constraints
  static BoxConstraints getControlPanelConstraints(BuildContext context) {
    final screenWidth = getSafeAreaWidth(context);
    final isPortrait = ResponsiveUtils.isPortrait(context);

    if (isPortrait) {
      return BoxConstraints(
        maxWidth: screenWidth,
        maxHeight: 200,
        minHeight: 150,
      );
    } else {
      return BoxConstraints(
        maxWidth: screenWidth * 0.4,
        maxHeight: double.infinity,
        minWidth: 200,
      );
    }
  }
}

enum FontSizeType {
  small,
  medium,
  large,
  extraLarge,
  title,
}

class ResponsiveWidget extends StatelessWidget {
  final Widget? mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveWidget({
    Key? key,
    this.mobile,
    this.tablet,
    this.desktop,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (ResponsiveUtils.isLargeScreen(context) && desktop != null) {
      return desktop!;
    } else if (ResponsiveUtils.isMediumScreen(context) && tablet != null) {
      return tablet!;
    } else if (mobile != null) {
      return mobile!;
    }
    
    return Container(); // Fallback
  }
}
