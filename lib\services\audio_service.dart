import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';

enum SoundEffect {
  move,
  rotate,
  lineClear,
  hardDrop,
  gameOver,
  levelUp,
}

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _soundEnabled = true;
  double _volume = 0.7;

  // Sound frequencies for different effects (in Hz)
  static const Map<SoundEffect, List<double>> _soundFrequencies = {
    SoundEffect.move: [200, 150],
    SoundEffect.rotate: [300, 250, 200],
    SoundEffect.lineClear: [400, 500, 600, 700],
    SoundEffect.hardDrop: [150, 100],
    SoundEffect.gameOver: [200, 150, 100, 80],
    SoundEffect.levelUp: [400, 500, 600, 800, 1000],
  };

  // Sound durations (in milliseconds)
  static const Map<SoundEffect, int> _soundDurations = {
    SoundEffect.move: 100,
    SoundEffect.rotate: 150,
    SoundEffect.lineClear: 500,
    SoundEffect.hardDrop: 200,
    SoundEffect.gameOver: 1000,
    SoundEffect.levelUp: 800,
  };

  bool get soundEnabled => _soundEnabled;
  double get volume => _volume;

  void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
  }

  void setVolume(double volume) {
    _volume = volume.clamp(0.0, 1.0);
  }

  Future<void> playSound(SoundEffect effect) async {
    if (!_soundEnabled) return;

    try {
      // Try to play from assets first
      String assetPath = _getAssetPath(effect);
      await _audioPlayer.play(AssetSource(assetPath));
    } catch (e) {
      // If asset doesn't exist, generate programmatic sound
      await _playProgrammaticSound(effect);
    }
  }

  String _getAssetPath(SoundEffect effect) {
    switch (effect) {
      case SoundEffect.move:
        return 'sounds/move.wav';
      case SoundEffect.rotate:
        return 'sounds/rotate.wav';
      case SoundEffect.lineClear:
        return 'sounds/line_clear.wav';
      case SoundEffect.hardDrop:
        return 'sounds/hard_drop.wav';
      case SoundEffect.gameOver:
        return 'sounds/game_over.wav';
      case SoundEffect.levelUp:
        return 'sounds/level_up.wav';
    }
  }

  Future<void> _playProgrammaticSound(SoundEffect effect) async {
    // Generate simple beep sounds using system feedback
    List<double> frequencies = _soundFrequencies[effect] ?? [400];
    int duration = _soundDurations[effect] ?? 100;

    for (int i = 0; i < frequencies.length; i++) {
      // Use haptic feedback for tactile response
      if (_soundEnabled) {
        switch (effect) {
          case SoundEffect.move:
            HapticFeedback.selectionClick();
            break;
          case SoundEffect.rotate:
            HapticFeedback.lightImpact();
            break;
          case SoundEffect.lineClear:
            HapticFeedback.mediumImpact();
            break;
          case SoundEffect.hardDrop:
            HapticFeedback.heavyImpact();
            break;
          case SoundEffect.gameOver:
            HapticFeedback.heavyImpact();
            break;
          case SoundEffect.levelUp:
            HapticFeedback.mediumImpact();
            break;
        }
      }

      // Add small delay between multiple tones
      if (i < frequencies.length - 1) {
        await Future.delayed(Duration(milliseconds: duration ~/ frequencies.length));
      }
    }
  }

  // Play a sequence of sounds for special effects
  Future<void> playLineClearSequence(int linesCleared) async {
    if (!_soundEnabled) return;

    switch (linesCleared) {
      case 1:
        await playSound(SoundEffect.lineClear);
        break;
      case 2:
        await playSound(SoundEffect.lineClear);
        await Future.delayed(const Duration(milliseconds: 100));
        await playSound(SoundEffect.lineClear);
        break;
      case 3:
        for (int i = 0; i < 3; i++) {
          await playSound(SoundEffect.lineClear);
          await Future.delayed(const Duration(milliseconds: 80));
        }
        break;
      case 4: // Tetris!
        for (int i = 0; i < 4; i++) {
          await playSound(SoundEffect.lineClear);
          await Future.delayed(const Duration(milliseconds: 60));
        }
        await Future.delayed(const Duration(milliseconds: 200));
        await playSound(SoundEffect.levelUp);
        break;
    }
  }

  // Play level up sound with celebration
  Future<void> playLevelUpCelebration() async {
    if (!_soundEnabled) return;

    await playSound(SoundEffect.levelUp);
    
    // Add extra haptic feedback for level up
    await Future.delayed(const Duration(milliseconds: 100));
    HapticFeedback.heavyImpact();
    await Future.delayed(const Duration(milliseconds: 100));
    HapticFeedback.heavyImpact();
  }

  // Play game over sequence
  Future<void> playGameOverSequence() async {
    if (!_soundEnabled) return;

    await playSound(SoundEffect.gameOver);
    
    // Add dramatic haptic feedback
    await Future.delayed(const Duration(milliseconds: 200));
    HapticFeedback.heavyImpact();
    await Future.delayed(const Duration(milliseconds: 300));
    HapticFeedback.heavyImpact();
  }

  void dispose() {
    _audioPlayer.dispose();
  }
}

// Extension to make playing sounds easier
extension GameSounds on AudioService {
  void playMoveSound() => playSound(SoundEffect.move);
  void playRotateSound() => playSound(SoundEffect.rotate);
  void playHardDropSound() => playSound(SoundEffect.hardDrop);
}
