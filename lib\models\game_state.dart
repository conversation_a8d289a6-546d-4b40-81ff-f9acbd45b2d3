import 'dart:ui';
import 'tetromino.dart';

enum GameStatus { playing, paused, gameOver }

class GameState {
  static const int gridWidth = 10;
  static const int gridHeight = 20;
  
  List<List<Color?>> grid;
  Tetromino? currentPiece;
  Tetromino? nextPiece;
  int score;
  int level;
  int linesCleared;
  GameStatus status;
  double dropTimer;
  double dropInterval;
  
  GameState({
    List<List<Color?>>? grid,
    this.currentPiece,
    this.nextPiece,
    this.score = 0,
    this.level = 1,
    this.linesCleared = 0,
    this.status = GameStatus.playing,
    this.dropTimer = 0.0,
    this.dropInterval = 1.0,
  }) : grid = grid ?? List.generate(
         gridHeight, 
         (i) => List.filled(gridWidth, null)
       );

  // Create a copy of the game state
  GameState copy() {
    return GameState(
      grid: grid.map((row) => List<Color?>.from(row)).toList(),
      currentPiece: currentPiece?.copy(),
      nextPiece: nextPiece?.copy(),
      score: score,
      level: level,
      linesCleared: linesCleared,
      status: status,
      dropTimer: dropTimer,
      dropInterval: dropInterval,
    );
  }

  // Reset the game state
  void reset() {
    grid = List.generate(gridHeight, (i) => List.filled(gridWidth, null));
    currentPiece = null;
    nextPiece = null;
    score = 0;
    level = 1;
    linesCleared = 0;
    status = GameStatus.playing;
    dropTimer = 0.0;
    dropInterval = 1.0;
  }

  // Check if a position is valid for a piece
  bool isValidPosition(Tetromino piece, int newX, int newY, int newRotation) {
    Tetromino testPiece = piece.copy();
    testPiece.x = newX;
    testPiece.y = newY;
    testPiece.rotation = newRotation;
    
    List<List<int>> positions = testPiece.getPositions();
    
    for (List<int> pos in positions) {
      int row = pos[0];
      int col = pos[1];
      
      // Check boundaries
      if (row < 0 || row >= gridHeight || col < 0 || col >= gridWidth) {
        return false;
      }
      
      // Check collision with existing pieces
      if (grid[row][col] != null) {
        return false;
      }
    }
    
    return true;
  }

  // Place the current piece on the grid
  void placePiece() {
    if (currentPiece == null) return;
    
    List<List<int>> positions = currentPiece!.getPositions();
    
    for (List<int> pos in positions) {
      int row = pos[0];
      int col = pos[1];
      
      if (row >= 0 && row < gridHeight && col >= 0 && col < gridWidth) {
        grid[row][col] = currentPiece!.color;
      }
    }
  }

  // Check for completed lines and clear them
  List<int> clearLines() {
    List<int> clearedLines = [];
    
    for (int row = gridHeight - 1; row >= 0; row--) {
      bool isComplete = true;
      for (int col = 0; col < gridWidth; col++) {
        if (grid[row][col] == null) {
          isComplete = false;
          break;
        }
      }
      
      if (isComplete) {
        clearedLines.add(row);
        // Remove the completed line
        grid.removeAt(row);
        // Add a new empty line at the top
        grid.insert(0, List.filled(gridWidth, null));
        row++; // Check the same row again since we removed one
      }
    }
    
    return clearedLines;
  }

  // Update score based on cleared lines
  void updateScore(int clearedLinesCount) {
    const List<int> lineScores = [0, 40, 100, 300, 1200];
    
    if (clearedLinesCount > 0 && clearedLinesCount <= 4) {
      score += lineScores[clearedLinesCount] * level;
      linesCleared += clearedLinesCount;
      
      // Increase level every 10 lines
      int newLevel = (linesCleared ~/ 10) + 1;
      if (newLevel > level) {
        level = newLevel;
        // Increase drop speed
        dropInterval = (1.0 - (level - 1) * 0.1).clamp(0.1, 1.0);
      }
    }
  }

  // Check if the game is over
  bool isGameOver() {
    // Check if any piece in the top rows
    for (int col = 0; col < gridWidth; col++) {
      if (grid[0][col] != null || grid[1][col] != null) {
        return true;
      }
    }
    return false;
  }
}
