import 'package:shared_preferences/shared_preferences.dart';

class ScoreService {
  static const String _highScoreKey = 'tetris_high_score';
  static const String _totalLinesKey = 'tetris_total_lines';
  static const String _gamesPlayedKey = 'tetris_games_played';

  // Score calculation constants
  static const Map<int, int> _lineScores = {
    1: 40,    // Single
    2: 100,   // Double
    3: 300,   // Triple
    4: 1200,  // Tetris
  };

  static const int _softDropPoints = 1;
  static const int _hardDropPoints = 2;

  // Get high score
  static Future<int> getHighScore() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_highScoreKey) ?? 0;
  }

  // Save high score
  static Future<void> saveHighScore(int score) async {
    final prefs = await SharedPreferences.getInstance();
    final currentHigh = await getHighScore();
    if (score > currentHigh) {
      await prefs.setInt(_highScoreKey, score);
    }
  }

  // Get total lines cleared
  static Future<int> getTotalLines() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_totalLinesKey) ?? 0;
  }

  // Add lines to total
  static Future<void> addLines(int lines) async {
    final prefs = await SharedPreferences.getInstance();
    final currentTotal = await getTotalLines();
    await prefs.setInt(_totalLinesKey, currentTotal + lines);
  }

  // Get games played
  static Future<int> getGamesPlayed() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_gamesPlayedKey) ?? 0;
  }

  // Increment games played
  static Future<void> incrementGamesPlayed() async {
    final prefs = await SharedPreferences.getInstance();
    final currentCount = await getGamesPlayed();
    await prefs.setInt(_gamesPlayedKey, currentCount + 1);
  }

  // Calculate score for cleared lines
  static int calculateLineScore(int linesCleared, int level) {
    if (linesCleared <= 0 || linesCleared > 4) return 0;
    return _lineScores[linesCleared]! * level;
  }

  // Calculate level based on lines cleared
  static int calculateLevel(int totalLinesCleared) {
    return (totalLinesCleared ~/ 10) + 1;
  }

  // Calculate drop speed based on level
  static double calculateDropSpeed(int level) {
    // Speed increases with level, minimum 0.05 seconds
    double baseSpeed = 1.0;
    double speedMultiplier = 0.9;
    double speed = baseSpeed * (speedMultiplier * (level - 1));
    return speed.clamp(0.05, 1.0);
  }

  // Get score for soft drop
  static int getSoftDropScore() => _softDropPoints;

  // Get score for hard drop
  static int getHardDropScore(int cellsDropped) => cellsDropped * _hardDropPoints;

  // Reset all statistics
  static Future<void> resetStatistics() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_highScoreKey);
    await prefs.remove(_totalLinesKey);
    await prefs.remove(_gamesPlayedKey);
  }

  // Get all statistics
  static Future<Map<String, int>> getAllStatistics() async {
    return {
      'highScore': await getHighScore(),
      'totalLines': await getTotalLines(),
      'gamesPlayed': await getGamesPlayed(),
    };
  }
}

class GameStatistics {
  int score;
  int level;
  int linesCleared;
  int totalLinesCleared;
  double dropSpeed;
  DateTime gameStartTime;
  Duration gameDuration;

  GameStatistics({
    this.score = 0,
    this.level = 1,
    this.linesCleared = 0,
    this.totalLinesCleared = 0,
    this.dropSpeed = 1.0,
    DateTime? gameStartTime,
    this.gameDuration = Duration.zero,
  }) : gameStartTime = gameStartTime ?? DateTime.now();

  // Update statistics when lines are cleared
  void updateForClearedLines(int lines) {
    if (lines <= 0) return;

    linesCleared += lines;
    totalLinesCleared += lines;
    
    // Calculate score
    int lineScore = ScoreService.calculateLineScore(lines, level);
    score += lineScore;

    // Update level
    int newLevel = ScoreService.calculateLevel(totalLinesCleared);
    if (newLevel > level) {
      level = newLevel;
      dropSpeed = ScoreService.calculateDropSpeed(level);
    }
  }

  // Add soft drop points
  void addSoftDropScore() {
    score += ScoreService.getSoftDropScore();
  }

  // Add hard drop points
  void addHardDropScore(int cellsDropped) {
    score += ScoreService.getHardDropScore(cellsDropped);
  }

  // Update game duration
  void updateDuration() {
    gameDuration = DateTime.now().difference(gameStartTime);
  }

  // Get lines per minute
  double getLinesPerMinute() {
    if (gameDuration.inSeconds == 0) return 0.0;
    return (totalLinesCleared * 60.0) / gameDuration.inSeconds;
  }

  // Get score per minute
  double getScorePerMinute() {
    if (gameDuration.inSeconds == 0) return 0.0;
    return (score * 60.0) / gameDuration.inSeconds;
  }

  // Reset statistics for new game
  void reset() {
    score = 0;
    level = 1;
    linesCleared = 0;
    totalLinesCleared = 0;
    dropSpeed = 1.0;
    gameStartTime = DateTime.now();
    gameDuration = Duration.zero;
  }

  // Create a copy
  GameStatistics copy() {
    return GameStatistics(
      score: score,
      level: level,
      linesCleared: linesCleared,
      totalLinesCleared: totalLinesCleared,
      dropSpeed: dropSpeed,
      gameStartTime: gameStartTime,
      gameDuration: gameDuration,
    );
  }

  @override
  String toString() {
    return 'GameStatistics(score: $score, level: $level, lines: $totalLinesCleared)';
  }
}
