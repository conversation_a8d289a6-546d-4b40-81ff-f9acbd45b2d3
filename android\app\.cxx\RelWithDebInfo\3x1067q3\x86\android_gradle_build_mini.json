{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Desktop\\0000\\5\\a\\android\\app\\.cxx\\RelWithDebInfo\\3x1067q3\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Desktop\\0000\\5\\a\\android\\app\\.cxx\\RelWithDebInfo\\3x1067q3\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}