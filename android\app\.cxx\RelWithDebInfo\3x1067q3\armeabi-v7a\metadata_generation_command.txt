                        -HC:\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Desktop\0000\5\a\build\app\intermediates\cxx\RelWithDebInfo\3x1067q3\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Desktop\0000\5\a\build\app\intermediates\cxx\RelWithDebInfo\3x1067q3\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\Desktop\0000\5\a\android\app\.cxx\RelWithDebInfo\3x1067q3\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2