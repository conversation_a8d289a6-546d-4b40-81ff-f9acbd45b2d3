class GameConfig {
  // Grid dimensions
  static const int gridWidth = 10;
  static const int gridHeight = 20;
  
  // Game timing
  static const double initialDropInterval = 1.0; // seconds
  static const double minDropInterval = 0.05; // seconds
  static const double speedIncreaseRate = 0.1; // per level
  
  // Scoring
  static const Map<int, int> lineScores = {
    1: 40,    // Single
    2: 100,   // Double  
    3: 300,   // Triple
    4: 1200,  // Tetris
  };
  
  static const int softDropPoints = 1;
  static const int hardDropPoints = 2;
  static const int linesPerLevel = 10;
  
  // Controls
  static const double swipeThreshold = 50.0;
  static const double tapThreshold = 10.0;
  static const Duration repeatDelay = Duration(milliseconds: 150);
  static const Duration repeatInterval = Duration(milliseconds: 50);
  
  // Animation
  static const Duration lineClearDuration = Duration(milliseconds: 500);
  static const Duration pieceFallDuration = Duration(milliseconds: 100);
  static const Duration gameOverDuration = Duration(milliseconds: 1000);
  
  // Audio
  static const double defaultVolume = 0.7;
  static const Map<String, int> soundDurations = {
    'move': 100,
    'rotate': 150,
    'lineClear': 500,
    'hardDrop': 200,
    'gameOver': 1000,
    'levelUp': 800,
  };
  
  // Visual
  static const double cellBorderWidth = 1.0;
  static const double gridBorderWidth = 2.0;
  static const double shadowBlurRadius = 10.0;
  static const double shadowSpreadRadius = 2.0;
  
  // Responsive design
  static const double minCellSize = 15.0;
  static const double maxCellSize = 40.0;
  static const double portraitGameAreaRatio = 0.8;
  static const double landscapeGameAreaRatio = 0.5;
  
  // Performance
  static const int maxParticles = 50;
  static const Duration frameRate = Duration(milliseconds: 16); // ~60 FPS
  static const bool enableShadows = true;
  static const bool enableParticles = true;
  
  // Storage keys
  static const String highScoreKey = 'tetris_high_score';
  static const String totalLinesKey = 'tetris_total_lines';
  static const String gamesPlayedKey = 'tetris_games_played';
  static const String soundEnabledKey = 'tetris_sound_enabled';
  static const String volumeKey = 'tetris_volume';
  
  // Game states
  static const String gameStatePlaying = 'playing';
  static const String gameStatePaused = 'paused';
  static const String gameStateGameOver = 'gameOver';
  
  // Colors (as hex values for easy customization)
  static const Map<String, int> pieceColors = {
    'I': 0xFF00FFFF, // Cyan
    'O': 0xFFFFFF00, // Yellow
    'T': 0xFF800080, // Purple
    'S': 0xFF00FF00, // Green
    'Z': 0xFFFF0000, // Red
    'J': 0xFF0000FF, // Blue
    'L': 0xFFFFA500, // Orange
  };
  
  static const Map<String, int> uiColors = {
    'background': 0xFF000000,
    'gridBorder': 0xFFFFFFFF,
    'gridLine': 0x4DFFFFFF,
    'scoreBackground': 0xFF1A1A1A,
    'scoreBorder': 0xFF666666,
    'buttonBackground': 0xFF0066CC,
    'buttonText': 0xFFFFFFFF,
    'gameOverOverlay': 0xCC000000,
    'flashEffect': 0xFFFFFFFF,
  };
  
  // Debug settings
  static const bool debugMode = false;
  static const bool showFPS = false;
  static const bool showGridCoordinates = false;
  static const bool logGameEvents = false;
  
  // Feature flags
  static const bool enableGhostPiece = true;
  static const bool enableHoldPiece = false; // Future feature
  static const bool enableMultiplayer = false; // Future feature
  static const bool enableThemes = false; // Future feature
  
  // Validation methods
  static bool isValidGridPosition(int x, int y) {
    return x >= 0 && x < gridWidth && y >= 0 && y < gridHeight;
  }
  
  static double calculateDropSpeed(int level) {
    double speed = initialDropInterval - (level - 1) * speedIncreaseRate;
    return speed.clamp(minDropInterval, initialDropInterval);
  }
  
  static int calculateScore(int linesCleared, int level) {
    if (linesCleared <= 0 || linesCleared > 4) return 0;
    return lineScores[linesCleared]! * level;
  }
  
  static int calculateLevel(int totalLinesCleared) {
    return (totalLinesCleared ~/ linesPerLevel) + 1;
  }
  
  // Asset paths
  static const String soundsPath = 'assets/sounds/';
  static const String imagesPath = 'assets/images/';
  static const String fontsPath = 'assets/fonts/';
  
  static const Map<String, String> soundFiles = {
    'move': '${soundsPath}move.wav',
    'rotate': '${soundsPath}rotate.wav',
    'lineClear': '${soundsPath}line_clear.wav',
    'hardDrop': '${soundsPath}hard_drop.wav',
    'gameOver': '${soundsPath}game_over.wav',
    'levelUp': '${soundsPath}level_up.wav',
  };
}
