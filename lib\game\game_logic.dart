import 'dart:async';
import 'dart:math';
import '../models/game_state.dart';
import '../models/tetromino.dart';
import '../services/score_service.dart';

class GameLogic {
  GameState _gameState = GameState();
  Timer? _gameTimer;
  final Random _random = Random();

  // Callbacks for UI updates
  Function()? onStateChanged;
  Function(List<int>)? onLinesCleared;
  Function()? onGameOver;
  Function()? onPieceMove;
  Function()? onPieceRotate;

  GameState get gameState => _gameState;

  void startGame() {
    _gameState.reset();
    _spawnNewPiece();
    _startGameLoop();
    onStateChanged?.call();
  }

  void pauseGame() {
    if (_gameState.status == GameStatus.playing) {
      _gameState.status = GameStatus.paused;
      _gameTimer?.cancel();
      onStateChanged?.call();
    }
  }

  void resumeGame() {
    if (_gameState.status == GameStatus.paused) {
      _gameState.status = GameStatus.playing;
      _startGameLoop();
      onStateChanged?.call();
    }
  }

  void stopGame() {
    _gameTimer?.cancel();
    _gameState.status = GameStatus.gameOver;
    onGameOver?.call();
    onStateChanged?.call();
  }

  void _startGameLoop() {
    _gameTimer?.cancel();
    _gameTimer = Timer.periodic(Duration(milliseconds: 50), (timer) {
      _update(0.05); // 50ms = 0.05 seconds
    });
  }

  void _update(double deltaTime) {
    if (_gameState.status != GameStatus.playing) return;

    _gameState.dropTimer += deltaTime;

    if (_gameState.dropTimer >= _gameState.dropInterval) {
      _gameState.dropTimer = 0.0;
      _dropPiece();
    }
  }

  void _spawnNewPiece() {
    if (_gameState.nextPiece == null) {
      _gameState.nextPiece = _createRandomPiece();
    }

    _gameState.currentPiece = _gameState.nextPiece;
    _gameState.nextPiece = _createRandomPiece();

    // Check if the new piece can be placed
    if (_gameState.currentPiece != null &&
        !_gameState.isValidPosition(
          _gameState.currentPiece!,
          _gameState.currentPiece!.x,
          _gameState.currentPiece!.y,
          _gameState.currentPiece!.rotation,
        )) {
      stopGame();
      return;
    }

    onStateChanged?.call();
  }

  Tetromino _createRandomPiece() {
    final types = TetrominoType.values;
    final randomType = types[_random.nextInt(types.length)];
    return TetrominoFactory.create(randomType, x: 3, y: 0);
  }

  void _dropPiece() {
    if (_gameState.currentPiece == null) return;

    if (_gameState.isValidPosition(
      _gameState.currentPiece!,
      _gameState.currentPiece!.x,
      _gameState.currentPiece!.y + 1,
      _gameState.currentPiece!.rotation,
    )) {
      _gameState.currentPiece!.y++;
      onStateChanged?.call();
    } else {
      _placePieceAndSpawnNew();
    }
  }

  void _placePieceAndSpawnNew() {
    _gameState.placePiece();

    // Check for completed lines
    List<int> clearedLines = _gameState.clearLines();
    if (clearedLines.isNotEmpty) {
      _gameState.updateScore(clearedLines.length);
      onLinesCleared?.call(clearedLines);
    }

    // Check game over
    if (_gameState.isGameOver()) {
      stopGame();
      return;
    }

    _spawnNewPiece();
  }

  // Player input methods
  bool movePieceLeft() {
    if (_gameState.currentPiece == null ||
        _gameState.status != GameStatus.playing) {
      return false;
    }

    if (_gameState.isValidPosition(
      _gameState.currentPiece!,
      _gameState.currentPiece!.x - 1,
      _gameState.currentPiece!.y,
      _gameState.currentPiece!.rotation,
    )) {
      _gameState.currentPiece!.x--;
      onPieceMove?.call();
      onStateChanged?.call();
      return true;
    }
    return false;
  }

  bool movePieceRight() {
    if (_gameState.currentPiece == null ||
        _gameState.status != GameStatus.playing) {
      return false;
    }

    if (_gameState.isValidPosition(
      _gameState.currentPiece!,
      _gameState.currentPiece!.x + 1,
      _gameState.currentPiece!.y,
      _gameState.currentPiece!.rotation,
    )) {
      _gameState.currentPiece!.x++;
      onPieceMove?.call();
      onStateChanged?.call();
      return true;
    }
    return false;
  }

  bool movePieceDown() {
    if (_gameState.currentPiece == null ||
        _gameState.status != GameStatus.playing) {
      return false;
    }

    if (_gameState.isValidPosition(
      _gameState.currentPiece!,
      _gameState.currentPiece!.x,
      _gameState.currentPiece!.y + 1,
      _gameState.currentPiece!.rotation,
    )) {
      _gameState.currentPiece!.y++;
      onPieceMove?.call();
      onStateChanged?.call();
      return true;
    }
    return false;
  }

  bool rotatePiece() {
    if (_gameState.currentPiece == null ||
        _gameState.status != GameStatus.playing) {
      return false;
    }

    int newRotation = (_gameState.currentPiece!.rotation + 1) % 4;

    if (_gameState.isValidPosition(
      _gameState.currentPiece!,
      _gameState.currentPiece!.x,
      _gameState.currentPiece!.y,
      newRotation,
    )) {
      _gameState.currentPiece!.rotation = newRotation;
      onPieceRotate?.call();
      onStateChanged?.call();
      return true;
    }
    return false;
  }

  void hardDrop() {
    if (_gameState.currentPiece == null ||
        _gameState.status != GameStatus.playing) {
      return;
    }

    while (_gameState.isValidPosition(
      _gameState.currentPiece!,
      _gameState.currentPiece!.x,
      _gameState.currentPiece!.y + 1,
      _gameState.currentPiece!.rotation,
    )) {
      _gameState.currentPiece!.y++;
    }

    _placePieceAndSpawnNew();
  }

  void dispose() {
    _gameTimer?.cancel();
  }
}
