import 'dart:ui';

enum TetrominoType { I, O, T, S, Z, J, L }

class Tetromino {
  final TetrominoType type;
  final List<List<int>> shape;
  final Color color;
  int x;
  int y;
  int rotation;

  Tetromino({
    required this.type,
    required this.shape,
    required this.color,
    this.x = 0,
    this.y = 0,
    this.rotation = 0,
  });

  // Get the current rotated shape
  List<List<int>> get currentShape {
    List<List<int>> rotatedShape = List.from(shape);
    
    for (int i = 0; i < rotation; i++) {
      rotatedShape = _rotateMatrix(rotatedShape);
    }
    
    return rotatedShape;
  }

  // Rotate matrix 90 degrees clockwise
  List<List<int>> _rotateMatrix(List<List<int>> matrix) {
    int rows = matrix.length;
    int cols = matrix[0].length;
    List<List<int>> rotated = List.generate(cols, (i) => List.filled(rows, 0));
    
    for (int i = 0; i < rows; i++) {
      for (int j = 0; j < cols; j++) {
        rotated[j][rows - 1 - i] = matrix[i][j];
      }
    }
    
    return rotated;
  }

  // Create a copy of the tetromino
  Tetromino copy() {
    return Tetromino(
      type: type,
      shape: shape.map((row) => List<int>.from(row)).toList(),
      color: color,
      x: x,
      y: y,
      rotation: rotation,
    );
  }

  // Get all possible positions of the current piece
  List<List<int>> getPositions() {
    List<List<int>> positions = [];
    List<List<int>> currentShape = this.currentShape;
    
    for (int i = 0; i < currentShape.length; i++) {
      for (int j = 0; j < currentShape[i].length; j++) {
        if (currentShape[i][j] == 1) {
          positions.add([y + i, x + j]);
        }
      }
    }
    
    return positions;
  }
}

class TetrominoFactory {
  static const Map<TetrominoType, List<List<int>>> _shapes = {
    TetrominoType.I: [
      [0, 0, 0, 0],
      [1, 1, 1, 1],
      [0, 0, 0, 0],
      [0, 0, 0, 0],
    ],
    TetrominoType.O: [
      [1, 1],
      [1, 1],
    ],
    TetrominoType.T: [
      [0, 1, 0],
      [1, 1, 1],
      [0, 0, 0],
    ],
    TetrominoType.S: [
      [0, 1, 1],
      [1, 1, 0],
      [0, 0, 0],
    ],
    TetrominoType.Z: [
      [1, 1, 0],
      [0, 1, 1],
      [0, 0, 0],
    ],
    TetrominoType.J: [
      [1, 0, 0],
      [1, 1, 1],
      [0, 0, 0],
    ],
    TetrominoType.L: [
      [0, 0, 1],
      [1, 1, 1],
      [0, 0, 0],
    ],
  };

  static const Map<TetrominoType, Color> _colors = {
    TetrominoType.I: Color(0xFF00FFFF), // Cyan
    TetrominoType.O: Color(0xFFFFFF00), // Yellow
    TetrominoType.T: Color(0xFF800080), // Purple
    TetrominoType.S: Color(0xFF00FF00), // Green
    TetrominoType.Z: Color(0xFFFF0000), // Red
    TetrominoType.J: Color(0xFF0000FF), // Blue
    TetrominoType.L: Color(0xFFFFA500), // Orange
  };

  static Tetromino create(TetrominoType type, {int x = 3, int y = 0}) {
    return Tetromino(
      type: type,
      shape: _shapes[type]!.map((row) => List<int>.from(row)).toList(),
      color: _colors[type]!,
      x: x,
      y: y,
    );
  }

  static Tetromino createRandom({int x = 3, int y = 0}) {
    final types = TetrominoType.values;
    final randomType = types[(DateTime.now().millisecondsSinceEpoch) % types.length];
    return create(randomType, x: x, y: y);
  }
}
