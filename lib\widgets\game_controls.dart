import 'package:flutter/material.dart';
import '../game/game_logic.dart';
import '../models/game_state.dart';

class GameControls extends StatelessWidget {
  final GameLogic gameLogic;
  final VoidCallback? onPause;
  final VoidCallback? onRestart;

  const GameControls({
    Key? key,
    required this.gameLogic,
    this.onPause,
    this.onRestart,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Movement controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlButton(
                icon: Icons.keyboard_arrow_left,
                onPressed: () => gameLogic.movePieceLeft(),
                color: Colors.blue,
              ),
              _buildControlButton(
                icon: Icons.keyboard_arrow_down,
                onPressed: () => gameLogic.movePieceDown(),
                color: Colors.green,
              ),
              _buildControlButton(
                icon: Icons.keyboard_arrow_right,
                onPressed: () => gameLogic.movePieceRight(),
                color: Colors.blue,
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Action controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlButton(
                icon: Icons.rotate_right,
                onPressed: () => gameLogic.rotatePiece(),
                color: Colors.purple,
                label: 'تدوير',
              ),
              _buildControlButton(
                icon: Icons.keyboard_double_arrow_down,
                onPressed: () => gameLogic.hardDrop(),
                color: Colors.red,
                label: 'إسقاط',
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Game controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlButton(
                icon:
                    gameLogic.gameState.status == GameStatus.playing
                        ? Icons.pause
                        : Icons.play_arrow,
                onPressed: onPause,
                color: Colors.orange,
                label:
                    gameLogic.gameState.status == GameStatus.playing
                        ? 'إيقاف'
                        : 'تشغيل',
              ),
              _buildControlButton(
                icon: Icons.restart_alt,
                onPressed: onRestart,
                color: Colors.grey,
                label: 'إعادة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required Color color,
    String? label,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          decoration: BoxDecoration(
            color: color.withOpacity(0.8),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: onPressed,
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Icon(icon, color: Colors.white, size: 28),
              ),
            ),
          ),
        ),
        if (label != null) ...[
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ],
    );
  }
}

class TouchControls extends StatefulWidget {
  final GameLogic gameLogic;
  final Widget child;

  const TouchControls({Key? key, required this.gameLogic, required this.child})
    : super(key: key);

  @override
  State<TouchControls> createState() => _TouchControlsState();
}

class _TouchControlsState extends State<TouchControls> {
  Offset? _panStart;
  static const double _swipeThreshold = 50.0;
  static const double _tapThreshold = 10.0;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanStart: (details) {
        _panStart = details.localPosition;
      },
      onPanEnd: (details) {
        if (_panStart == null) return;

        final delta = details.velocity.pixelsPerSecond;
        final distance = (details.localPosition - _panStart!).distance;

        // If it's a small movement, treat as tap
        if (distance < _tapThreshold) {
          widget.gameLogic.rotatePiece();
          return;
        }

        // Handle swipe gestures
        if (distance > _swipeThreshold) {
          final dx = details.localPosition.dx - _panStart!.dx;
          final dy = details.localPosition.dy - _panStart!.dy;

          if (dx.abs() > dy.abs()) {
            // Horizontal swipe
            if (dx > 0) {
              widget.gameLogic.movePieceRight();
            } else {
              widget.gameLogic.movePieceLeft();
            }
          } else {
            // Vertical swipe
            if (dy > 0) {
              // Swipe down - soft drop or hard drop based on velocity
              if (delta.dy > 1000) {
                widget.gameLogic.hardDrop();
              } else {
                widget.gameLogic.movePieceDown();
              }
            }
          }
        }

        _panStart = null;
      },
      onTap: () {
        // Simple tap rotates the piece
        widget.gameLogic.rotatePiece();
      },
      child: widget.child,
    );
  }
}
