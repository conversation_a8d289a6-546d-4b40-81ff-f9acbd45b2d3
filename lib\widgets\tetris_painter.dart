import 'package:flutter/material.dart';
import '../models/game_state.dart';
import '../models/tetromino.dart';

class TetrisPainter extends CustomPainter {
  final GameState gameState;
  final double cellSize;
  final List<int>? flashingLines;
  final double flashOpacity;

  TetrisPainter({
    required this.gameState,
    required this.cellSize,
    this.flashingLines,
    this.flashOpacity = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    _drawGrid(canvas);
    _drawPlacedPieces(canvas);
    _drawCurrentPiece(canvas);
    _drawGhostPiece(canvas);
    
    if (flashingLines != null && flashingLines!.isNotEmpty) {
      _drawFlashingLines(canvas);
    }
  }

  void _drawGrid(Canvas canvas) {
    final paint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw vertical lines
    for (int i = 0; i <= GameState.gridWidth; i++) {
      double x = i * cellSize;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, GameState.gridHeight * cellSize),
        paint,
      );
    }

    // Draw horizontal lines
    for (int i = 0; i <= GameState.gridHeight; i++) {
      double y = i * cellSize;
      canvas.drawLine(
        Offset(0, y),
        Offset(GameState.gridWidth * cellSize, y),
        paint,
      );
    }
  }

  void _drawPlacedPieces(Canvas canvas) {
    for (int row = 0; row < GameState.gridHeight; row++) {
      for (int col = 0; col < GameState.gridWidth; col++) {
        Color? cellColor = gameState.grid[row][col];
        if (cellColor != null) {
          _drawCell(canvas, col, row, cellColor);
        }
      }
    }
  }

  void _drawCurrentPiece(Canvas canvas) {
    if (gameState.currentPiece == null) return;

    Tetromino piece = gameState.currentPiece!;
    List<List<int>> positions = piece.getPositions();

    for (List<int> pos in positions) {
      int row = pos[0];
      int col = pos[1];
      
      if (row >= 0 && row < GameState.gridHeight && 
          col >= 0 && col < GameState.gridWidth) {
        _drawCell(canvas, col, row, piece.color);
      }
    }
  }

  void _drawGhostPiece(Canvas canvas) {
    if (gameState.currentPiece == null) return;

    Tetromino ghostPiece = gameState.currentPiece!.copy();
    
    // Move ghost piece down until it hits something
    while (gameState.isValidPosition(
      ghostPiece,
      ghostPiece.x,
      ghostPiece.y + 1,
      ghostPiece.rotation,
    )) {
      ghostPiece.y++;
    }

    // Don't draw ghost if it's at the same position as current piece
    if (ghostPiece.y == gameState.currentPiece!.y) return;

    List<List<int>> positions = ghostPiece.getPositions();
    Color ghostColor = gameState.currentPiece!.color.withOpacity(0.3);

    for (List<int> pos in positions) {
      int row = pos[0];
      int col = pos[1];
      
      if (row >= 0 && row < GameState.gridHeight && 
          col >= 0 && col < GameState.gridWidth) {
        _drawCell(canvas, col, row, ghostColor, isGhost: true);
      }
    }
  }

  void _drawFlashingLines(Canvas canvas) {
    if (flashingLines == null) return;

    final paint = Paint()
      ..color = Colors.white.withOpacity(flashOpacity)
      ..style = PaintingStyle.fill;

    for (int row in flashingLines!) {
      if (row >= 0 && row < GameState.gridHeight) {
        canvas.drawRect(
          Rect.fromLTWH(
            0,
            row * cellSize,
            GameState.gridWidth * cellSize,
            cellSize,
          ),
          paint,
        );
      }
    }
  }

  void _drawCell(Canvas canvas, int col, int row, Color color, {bool isGhost = false}) {
    double x = col * cellSize;
    double y = row * cellSize;

    // Main cell color
    final fillPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawRect(
      Rect.fromLTWH(x, y, cellSize, cellSize),
      fillPaint,
    );

    if (!isGhost) {
      // Add 3D effect with highlights and shadows
      final highlightPaint = Paint()
        ..color = color.withOpacity(0.8)
        ..strokeWidth = 2.0
        ..style = PaintingStyle.stroke;

      final shadowPaint = Paint()
        ..color = Colors.black.withOpacity(0.3)
        ..strokeWidth = 2.0
        ..style = PaintingStyle.stroke;

      // Top and left highlights
      canvas.drawLine(
        Offset(x, y),
        Offset(x + cellSize, y),
        highlightPaint,
      );
      canvas.drawLine(
        Offset(x, y),
        Offset(x, y + cellSize),
        highlightPaint,
      );

      // Bottom and right shadows
      canvas.drawLine(
        Offset(x, y + cellSize),
        Offset(x + cellSize, y + cellSize),
        shadowPaint,
      );
      canvas.drawLine(
        Offset(x + cellSize, y),
        Offset(x + cellSize, y + cellSize),
        shadowPaint,
      );
    }

    // Border
    final borderPaint = Paint()
      ..color = isGhost ? color.withOpacity(0.5) : Colors.black.withOpacity(0.8)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    canvas.drawRect(
      Rect.fromLTWH(x, y, cellSize, cellSize),
      borderPaint,
    );
  }

  @override
  bool shouldRepaint(covariant TetrisPainter oldDelegate) {
    return oldDelegate.gameState != gameState ||
           oldDelegate.flashingLines != flashingLines ||
           oldDelegate.flashOpacity != flashOpacity;
  }
}

class NextPiecePainter extends CustomPainter {
  final Tetromino? nextPiece;
  final double cellSize;

  NextPiecePainter({
    required this.nextPiece,
    required this.cellSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (nextPiece == null) return;

    List<List<int>> shape = nextPiece!.currentShape;
    Color color = nextPiece!.color;

    // Calculate offset to center the piece
    double offsetX = (size.width - shape[0].length * cellSize) / 2;
    double offsetY = (size.height - shape.length * cellSize) / 2;

    for (int row = 0; row < shape.length; row++) {
      for (int col = 0; col < shape[row].length; col++) {
        if (shape[row][col] == 1) {
          double x = offsetX + col * cellSize;
          double y = offsetY + row * cellSize;

          // Main cell color
          final fillPaint = Paint()
            ..color = color
            ..style = PaintingStyle.fill;

          canvas.drawRect(
            Rect.fromLTWH(x, y, cellSize, cellSize),
            fillPaint,
          );

          // Border
          final borderPaint = Paint()
            ..color = Colors.black.withOpacity(0.8)
            ..strokeWidth = 1.0
            ..style = PaintingStyle.stroke;

          canvas.drawRect(
            Rect.fromLTWH(x, y, cellSize, cellSize),
            borderPaint,
          );
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant NextPiecePainter oldDelegate) {
    return oldDelegate.nextPiece != nextPiece;
  }
}
