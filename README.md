# a

A new Flutter project.

## Getting Started

# Tetris Game - لعبة تيتريس

لعبة تيتريس كلاسيكية مطورة باستخدام Flutter مع دعم كامل للهاتف المحمول والويب.

## المميزات

### 🎮 اللعب الأساسي
- شبكة لعب بمقاس 10x20
- 7 أنواع من القطع (I, O, T, S, Z, L, J)
- تحريك القطع يمين/يسار/أسفل
- تدوير القطع
- حذف الصفوف المكتملة تلقائياً

### 🏆 نظام النقاط
- عداد للنقاط يزداد مع حذف الصفوف
- نظام مستويات متدرج
- زيادة السرعة مع كل مستوى
- حفظ أعلى نتيجة محلياً

### 🎵 الصوتيات والتأثيرات
- أصوات للحركة والتدوير
- أصوات خاصة لحذف الصفوف
- تأثيرات بصرية للأنيميشن
- ردود فعل لمسية (Haptic Feedback)

### 📱 التحكم
- دعم كامل للمس
- إيماءات السحب للتحريك
- النقر للتدوير
- أزرار تحكم مرئية
- دعم الاتجاه العمودي والأفقي

### 🎨 الواجهة
- تصميم أنيق ومتجاوب
- دعم الوضع المظلم
- أنيميشن سلس للحركة
- تأثيرات بصرية للقطع
- واجهة متكيفة مع أحجام الشاشات المختلفة

## كيفية اللعب

### التحكم باللمس
- **اسحب يميناً/يساراً**: تحريك القطعة
- **اسحب لأسفل**: إسقاط سريع
- **اضغط**: تدوير القطعة
- **اسحب لأسفل بسرعة**: إسقاط فوري

### التحكم بالأزرار
- **أسهم التحريك**: تحريك القطعة
- **زر التدوير**: تدوير القطعة
- **زر الإسقاط**: إسقاط فوري
- **زر الإيقاف/التشغيل**: إيقاف/استئناف اللعبة
- **زر الإعادة**: بدء لعبة جديدة

### نظام النقاط
- **صف واحد**: 40 × المستوى
- **صفان**: 100 × المستوى
- **ثلاثة صفوف**: 300 × المستوى
- **أربعة صفوف (Tetris)**: 1200 × المستوى

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي
- **CustomPainter**: لرسم الشبكة والقطع
- **SharedPreferences**: لحفظ النتائج
- **AudioPlayers**: للصوتيات
- **Animation Controllers**: للتأثيرات البصرية

## هيكل المشروع

```
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
│   ├── tetromino.dart       # نموذج القطع
│   └── game_state.dart      # حالة اللعبة
├── game/                    # منطق اللعبة
│   └── game_logic.dart      # المنطق الأساسي
├── widgets/                 # عناصر الواجهة
│   ├── tetris_painter.dart  # رسم اللعبة
│   ├── game_controls.dart   # أزرار التحكم
│   └── animated_effects.dart # التأثيرات المتحركة
├── screens/                 # الشاشات
│   └── game_screen.dart     # شاشة اللعبة
├── services/                # الخدمات
│   ├── audio_service.dart   # خدمة الصوت
│   └── score_service.dart   # خدمة النقاط
└── utils/                   # أدوات مساعدة
    └── responsive_utils.dart # أدوات التجاوب
```

## التشغيل

### المتطلبات
- Flutter SDK (3.7.2 أو أحدث)
- Dart SDK
- متصفح ويب أو محاكي Android/iOS

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd tetris-game
```

2. **تحميل التبعيات**
```bash
flutter pub get
```

3. **تشغيل اللعبة**
```bash
# للويب
flutter run -d chrome

# للهاتف
flutter run

# للسطح المكتب (Windows)
flutter run -d windows
```

## الإعدادات

### تخصيص اللعبة
يمكن تعديل إعدادات اللعبة في الملفات التالية:
- `lib/models/game_state.dart`: أبعاد الشبكة وسرعة اللعبة
- `lib/services/score_service.dart`: نظام النقاط
- `lib/widgets/tetris_painter.dart`: الألوان والتأثيرات البصرية

### إضافة أصوات مخصصة
ضع ملفات الصوت في مجلد `assets/sounds/` وحدث `pubspec.yaml`:
- `move.wav`: صوت الحركة
- `rotate.wav`: صوت التدوير
- `line_clear.wav`: صوت حذف الصف
- `game_over.wav`: صوت انتهاء اللعبة

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الشكر والتقدير

- مطوري Flutter لإطار العمل الرائع
- مجتمع Flutter للدعم والمساعدة
- لعبة Tetris الأصلية لإلهام هذا المشروع
