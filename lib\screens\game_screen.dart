import 'package:flutter/material.dart';
import '../game/game_logic.dart';
import '../models/game_state.dart';
import '../widgets/tetris_painter.dart';
import '../widgets/game_controls.dart';
import '../services/audio_service.dart';
import '../services/score_service.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({Key? key}) : super(key: key);

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with TickerProviderStateMixin {
  late GameLogic _gameLogic;
  late AudioService _audioService;
  late AnimationController _flashController;
  late Animation<double> _flashAnimation;

  List<int>? _flashingLines;
  int _highScore = 0;
  bool _showGameOver = false;

  @override
  void initState() {
    super.initState();
    _initializeGame();
    _loadHighScore();
  }

  void _initializeGame() {
    _gameLogic = GameLogic();
    _audioService = AudioService();

    // Setup flash animation for line clearing
    _flashController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _flashAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _flashController, curve: Curves.easeInOut),
    );

    // Setup game callbacks
    _gameLogic.onStateChanged = () {
      if (mounted) setState(() {});
    };

    _gameLogic.onLinesCleared = (lines) {
      _flashingLines = lines;
      _flashController.forward().then((_) {
        _flashController.reset();
        _flashingLines = null;
        if (mounted) setState(() {});
      });
      _audioService.playLineClearSequence(lines.length);
    };

    _gameLogic.onGameOver = () {
      _showGameOver = true;
      _audioService.playGameOverSequence();
      _saveHighScore();
      if (mounted) setState(() {});
    };

    _gameLogic.onPieceMove = () {
      _audioService.playMoveSound();
    };

    _gameLogic.onPieceRotate = () {
      _audioService.playRotateSound();
    };
  }

  Future<void> _loadHighScore() async {
    final highScore = await ScoreService.getHighScore();
    setState(() {
      _highScore = highScore;
    });
  }

  Future<void> _saveHighScore() async {
    await ScoreService.saveHighScore(_gameLogic.gameState.score);
    await _loadHighScore();
  }

  void _startNewGame() {
    setState(() {
      _showGameOver = false;
    });
    _gameLogic.startGame();
    ScoreService.incrementGamesPlayed();
  }

  void _pauseResumeGame() {
    if (_gameLogic.gameState.status == GameStatus.playing) {
      _gameLogic.pauseGame();
    } else if (_gameLogic.gameState.status == GameStatus.paused) {
      _gameLogic.resumeGame();
    }
  }

  @override
  void dispose() {
    _gameLogic.dispose();
    _flashController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            final isPortrait = constraints.maxHeight > constraints.maxWidth;

            if (isPortrait) {
              return _buildPortraitLayout(constraints);
            } else {
              return _buildLandscapeLayout(constraints);
            }
          },
        ),
      ),
    );
  }

  Widget _buildPortraitLayout(BoxConstraints constraints) {
    final gameAreaHeight = constraints.maxHeight * 0.6;
    final cellSize = gameAreaHeight / GameState.gridHeight;

    return Column(
      children: [
        // Header with score and info
        _buildHeader(),

        // Game area
        Expanded(
          child: Row(
            children: [
              // Next piece preview
              _buildNextPieceArea(cellSize * 0.8),

              // Main game grid
              Expanded(child: Center(child: _buildGameArea(cellSize))),

              // Game info
              _buildGameInfo(),
            ],
          ),
        ),

        // Controls
        GameControls(
          gameLogic: _gameLogic,
          onPause: _pauseResumeGame,
          onRestart: _startNewGame,
        ),
      ],
    );
  }

  Widget _buildLandscapeLayout(BoxConstraints constraints) {
    final gameAreaWidth = constraints.maxWidth * 0.5;
    final cellSize = gameAreaWidth / GameState.gridWidth;

    return Row(
      children: [
        // Left side - Game area
        Expanded(
          flex: 3,
          child: Column(
            children: [
              _buildHeader(),
              Expanded(child: Center(child: _buildGameArea(cellSize))),
            ],
          ),
        ),

        // Right side - Info and controls
        Expanded(
          flex: 2,
          child: Column(
            children: [
              _buildNextPieceArea(cellSize * 0.6),
              _buildGameInfo(),
              Expanded(
                child: GameControls(
                  gameLogic: _gameLogic,
                  onPause: _pauseResumeGame,
                  onRestart: _startNewGame,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildScoreCard('النقاط', _gameLogic.gameState.score),
          _buildScoreCard('المستوى', _gameLogic.gameState.level),
          _buildScoreCard('الخطوط', _gameLogic.gameState.linesCleared),
          _buildScoreCard('أعلى نتيجة', _highScore),
        ],
      ),
    );
  }

  Widget _buildScoreCard(String label, int value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value.toString(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameArea(double cellSize) {
    return Stack(
      children: [
        // Game grid
        TouchControls(
          gameLogic: _gameLogic,
          child: Container(
            width: GameState.gridWidth * cellSize,
            height: GameState.gridHeight * cellSize,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.white, width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.3),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: AnimatedBuilder(
              animation: _flashAnimation,
              builder: (context, child) {
                return CustomPaint(
                  painter: TetrisPainter(
                    gameState: _gameLogic.gameState,
                    cellSize: cellSize,
                    flashingLines: _flashingLines,
                    flashOpacity: _flashAnimation.value,
                  ),
                );
              },
            ),
          ),
        ),

        // Game over overlay
        if (_showGameOver) _buildGameOverOverlay(cellSize),
      ],
    );
  }

  Widget _buildNextPieceArea(double cellSize) {
    return Container(
      width: cellSize * 5,
      height: cellSize * 5,
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          const Padding(
            padding: EdgeInsets.all(8.0),
            child: Text(
              'القطعة التالية',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: CustomPaint(
              painter: NextPiecePainter(
                nextPiece: _gameLogic.gameState.nextPiece,
                cellSize: cellSize * 0.8,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameInfo() {
    return Container(
      width: 120,
      margin: const EdgeInsets.all(8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'معلومات اللعبة',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildInfoRow(
            'السرعة',
            '${_gameLogic.gameState.dropInterval.toStringAsFixed(1)}s',
          ),
          const SizedBox(height: 8),
          _buildInfoRow('الحالة', _getStatusText()),
          const SizedBox(height: 12),
          _buildSoundToggle(),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 12),
        ),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildSoundToggle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          'الصوت',
          style: TextStyle(color: Colors.white70, fontSize: 12),
        ),
        Switch(
          value: _audioService.soundEnabled,
          onChanged: (value) {
            setState(() {
              _audioService.setSoundEnabled(value);
            });
          },
          activeColor: Colors.blue,
        ),
      ],
    );
  }

  String _getStatusText() {
    switch (_gameLogic.gameState.status) {
      case GameStatus.playing:
        return 'يلعب';
      case GameStatus.paused:
        return 'متوقف';
      case GameStatus.gameOver:
        return 'انتهت';
    }
  }

  Widget _buildGameOverOverlay(double cellSize) {
    return Container(
      width: GameState.gridWidth * cellSize,
      height: GameState.gridHeight * cellSize,
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.red, width: 2),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'انتهت اللعبة!',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'النقاط: ${_gameLogic.gameState.score}',
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
              const SizedBox(height: 8),
              Text(
                'المستوى: ${_gameLogic.gameState.level}',
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
              const SizedBox(height: 8),
              Text(
                'الخطوط: ${_gameLogic.gameState.linesCleared}',
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
              if (_gameLogic.gameState.score == _highScore) ...[
                const SizedBox(height: 8),
                const Text(
                  '🎉 رقم قياسي جديد! 🎉',
                  style: TextStyle(
                    color: Colors.yellow,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _startNewGame,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 12,
                  ),
                ),
                child: const Text(
                  'لعب مرة أخرى',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
